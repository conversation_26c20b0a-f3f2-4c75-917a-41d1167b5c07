/**
 * 销售记录管理器
 * 处理销售记录的业务逻辑，包括与工作记录的关联
 */

import SalesRecordDB from './salesRecord.js'

class SalesManager {
	constructor() {
		this.salesDB = new SalesRecordDB()
	}

	/**
	 * 创建或更新销售记录
	 * @param {Object} salesData 销售记录数据
	 * @returns {Object} 操作结果
	 */
	async createOrUpdateSalesRecord(salesData) {
		try {
			let result

			if (salesData.id) {
				// 更新现有记录
				result = await this.salesDB.updateSalesRecord(salesData.id, salesData)
			} else {
				// 创建新记录
				result = await this.salesDB.createSalesRecord(salesData)
			}

			return {
				success: true,
				data: result,
				message: salesData.id ? '销售记录更新成功' : '销售记录创建成功'
			}
		} catch (error) {
			console.error('创建或更新销售记录失败:', error)
			return {
				success: false,
				message: error.message || '操作失败'
			}
		}
	}

	/**
	 * 创建或更新单客户销售记录
	 * @param {String} date 日期
	 * @param {Object} customerData 客户数据
	 * @returns {Object} 操作结果
	 */
	async createSingleCustomerSalesRecord(date, customerData) {
		try {
			// 🔧 修复：检查是否存在该日期的销售记录，如果存在则更新
			let existingRecord = null
			try {
				const existingRecords = this.salesDB.getSalesRecordsByDate(date)
				if (existingRecords.length > 0) {
					// 找到单客户模式的记录或第一条记录
					existingRecord = existingRecords.find(record =>
						record.sales_mode === 'single_customer'
					) || existingRecords[0]
					console.log('🔄 [SalesManager] 找到现有销售记录，将进行更新:', existingRecord.id)
				}
			} catch (error) {
				console.log('🔍 [SalesManager] 检查现有记录时出错，将创建新记录:', error.message)
			}

			const salesData = {
				id: existingRecord ? existingRecord.id : null, // 🔧 修复：传递现有记录ID
				date: date,
				sales_mode: 'single_customer',
				customer_name: customerData.customer_name || '',
				selling_price: parseFloat(customerData.selling_price) || 0,
				production: parseFloat(customerData.production) || 0,
				other_income: parseFloat(customerData.other_income) || 0,
				other_cost: parseFloat(customerData.other_cost) || 0,
				notes: customerData.notes || '',
				allocation_strategy: 'auto'
			}

			console.log('💾 [SalesManager] 保存单客户记录:', {
				operation: existingRecord ? '更新' : '创建',
				recordId: salesData.id,
				date: salesData.date,
				customer: salesData.customer_name,
				production: salesData.production
			})

			return await this.createOrUpdateSalesRecord(salesData)
		} catch (error) {
			console.error('创建单客户销售记录失败:', error)
			return {
				success: false,
				message: error.message || '创建单客户销售记录失败'
			}
		}
	}

	/**
	 * 创建多客户销售记录
	 * @param {String} date 日期
	 * @param {Array} customersData 客户数据列表
	 * @returns {Object} 操作结果
	 */
	async createMultipleCustomersSalesRecord(date, customersData) {
		try {
			if (!Array.isArray(customersData) || customersData.length === 0) {
				throw new Error('多客户模式需要提供客户数据列表')
			}

			const salesData = {
				date: date,
				sales_mode: 'multiple_customers',
				customer_details: customersData.map((customer, index) => ({
					customer_name: customer.customer_name || '',
					selling_price: parseFloat(customer.selling_price) || 0,
					production: parseFloat(customer.production) || 0,
					other_income: parseFloat(customer.other_income) || 0,
					other_cost: parseFloat(customer.other_cost) || 0,
					notes: customer.notes || '',
					allocation_priority: customer.allocation_priority || (index + 1),
					time_period_allocation: customer.time_period_allocation || {}
				})),
				allocation_strategy: 'auto',
				notes: `多客户销售 - ${customersData.length}个客户`
			}

			return await this.createOrUpdateSalesRecord(salesData)
		} catch (error) {
			console.error('创建多客户销售记录失败:', error)
			return {
				success: false,
				message: error.message || '创建多客户销售记录失败'
			}
		}
	}

	/**
	 * 根据工作记录获取销售信息
	 * @param {Object} workRecord 工作记录
	 * @returns {Object} 销售信息
	 */
	getSalesInfoByWorkRecord(workRecord) {
		try {
			const salesRecords = this.salesDB.getSalesRecordsByWorkRecordId(workRecord.id)
			const production = this.salesDB.calculateProductionFromWorkRecord(workRecord)

			return {
				workRecord: workRecord,
				salesRecords: salesRecords,
				production: production,
				totalSalesIncome: salesRecords.reduce((sum, record) => sum + record.total_income, 0),
				totalSalesCost: salesRecords.reduce((sum, record) => sum + record.total_cost, 0),
				totalGrossProfit: salesRecords.reduce((sum, record) => sum + record.gross_profit, 0)
			}
		} catch (error) {
			console.error('获取工作记录销售信息失败:', error)
			return {
				workRecord: workRecord,
				salesRecords: [],
				production: 0,
				totalSalesIncome: 0,
				totalSalesCost: 0,
				totalGrossProfit: 0
			}
		}
	}

	/**
	 * 根据日期范围获取销售记录
	 * @param {String} startDate 开始日期
	 * @param {String} endDate 结束日期
	 * @returns {Array} 销售记录列表（包含工作记录信息）
	 */
	getSalesRecordsByDateRange(startDate, endDate) {
		try {
			const salesRecords = this.salesDB.getSalesRecordsByDateRange(startDate, endDate)

			// 为每个销售记录添加工作记录信息
			return salesRecords.map(salesRecord => {
				const workRecord = this.salesDB.getWorkRecordById(salesRecord.work_record_id)
				return {
					...salesRecord,
					workRecord: workRecord,
					date: workRecord?.date || '',
					worker_name: workRecord?.worker_name || '',
					work_mode: workRecord?.work_mode || ''
				}
			})
		} catch (error) {
			console.error('根据日期范围获取销售记录失败:', error)
			return []
		}
	}

	/**
	 * 根据日期范围获取收入记录（新版本：按日期汇总）
	 * 实现业务规则：按日期显示，每个日期显示该日的总产量和销售情况
	 * @param {String} startDate 开始日期
	 * @param {String} endDate 结束日期
	 * @returns {Array} 收入记录列表
	 */
	getIncomeRecordsByDateRange(startDate, endDate) {
		try {
			console.log('获取收入记录（按日期汇总），日期范围:', startDate, '到', endDate)

			// 1. 获取日期范围内的所有工作记录，按日期分组
			const allWorkRecords = uni.getStorageSync('workRecords') || []
			const workRecordsByDate = {}

			allWorkRecords.forEach(workRecord => {
				const recordDate = new Date(workRecord.date)
				const start = new Date(startDate)
				const end = new Date(endDate)

				if (recordDate >= start && recordDate <= end) {
					if (!workRecordsByDate[workRecord.date]) {
						workRecordsByDate[workRecord.date] = []
					}
					workRecordsByDate[workRecord.date].push(workRecord)
				}
			})

			console.log('日期范围内的唯一日期:', Object.keys(workRecordsByDate).length, '天')

			// 2. 为每个日期获取对应的销售记录
			const incomeRecords = []

			for (const [date, workRecords] of Object.entries(workRecordsByDate)) {
				// 计算该日期的总产量和总成本
				const dailyTotalProduction = this.calculateDailyTotalProduction(date)
				const dailyTotalCost = this.calculateDailyTotalCost(date)

				// 获取该日期的销售记录
				const salesRecords = this.salesDB.getSalesRecordsByDate(date)

				if (salesRecords.length > 0) {
					// 有销售记录：汇总同一日期的多个销售记录为一条显示记录
					const totalSalesProduction = salesRecords.reduce((sum, record) => sum + (parseFloat(record.production) || 0), 0)
					const totalSalesIncome = salesRecords.reduce((sum, record) => sum + (parseFloat(record.total_income) || 0), 0)
					const totalOtherIncome = salesRecords.reduce((sum, record) => sum + (parseFloat(record.other_income) || 0), 0)
					const totalOtherCost = salesRecords.reduce((sum, record) => sum + (parseFloat(record.other_cost) || 0), 0)
					const customerNames = salesRecords.map(record => record.customer_name).filter(name => name).join(', ')

					// 🔧 修复：使用实际工作记录产量而不是销售记录中存储的产量
					// 这样当工作记录产量变化时，income页面能立即反映最新数据
					const displayProduction = dailyTotalProduction
					const averagePrice = displayProduction > 0 ? (totalSalesIncome / displayProduction) : 0

					console.log(`📊 [SalesManager] 日期${date}产量数据对比:`, {
						salesRecordProduction: totalSalesProduction,
						workRecordProduction: dailyTotalProduction,
						displayProduction: displayProduction
					})

					incomeRecords.push({
						id: `aggregated_${date}`, // 汇总记录的唯一ID
						date: date,
						workRecords: workRecords, // 该日期的所有工作记录
						salesRecords: salesRecords, // 该日期的所有销售记录
						dailyTotalProduction: dailyTotalProduction,
						dailyTotalCost: dailyTotalCost,
						worker_names: workRecords.map(wr => wr.worker_name).join(', '),
						// 汇总的销售数据
						production: displayProduction, // 使用工作记录的实际产量
						selling_price: averagePrice,
						customer_name: customerNames,
						customer_count: salesRecords.length,
						total_income: totalSalesIncome + totalOtherIncome,
						other_income: totalOtherIncome,
						other_cost: totalOtherCost,
						gross_profit: (totalSalesIncome + totalOtherIncome) - (dailyTotalCost + totalOtherCost),
						hasActualSales: true,
						isAggregated: true // 标记为汇总记录
					})
				} else {
					// 没有销售记录：创建一个占位符收入记录
					incomeRecords.push({
						id: `placeholder_${date}`,
						date: date,
						workRecords: workRecords,
						dailyTotalProduction: dailyTotalProduction,
						dailyTotalCost: dailyTotalCost,
						worker_names: workRecords.map(wr => wr.worker_name).join(', '),
						production: dailyTotalProduction, // 显示该日总产量
						selling_price: null, // 留白
						customer_name: null, // 留白
						other_cost: 0,
						total_income: 0,
						hasActualSales: false,
						isPlaceholder: true
					})
				}
			}

			console.log('生成的收入记录:', incomeRecords.length, '条')
			return incomeRecords

		} catch (error) {
			console.error('根据日期范围获取收入记录失败:', error)
			return []
		}
	}

	/**
	 * 计算某日期的总产量
	 * @param {String} date 日期 (YYYY-MM-DD)
	 * @returns {Number} 总产量
	 */
	calculateDailyTotalProduction(date) {
		const workRecords = this.getWorkRecordsByDate(date)
		return workRecords.reduce((total, workRecord) => {
			if (workRecord.work_mode === 'tea_picking') {
				const recordProduction = this.calculateWorkRecordProduction(workRecord)
				return total + recordProduction
			}
			return total
		}, 0)
	}

	/**
	 * 计算某日期的总成本
	 * @param {String} date 日期 (YYYY-MM-DD)
	 * @returns {Number} 总成本
	 */
	calculateDailyTotalCost(date) {
		const workRecords = this.getWorkRecordsByDate(date)
		return workRecords.reduce((total, workRecord) => {
			return total + (workRecord.total_earnings || 0)
		}, 0)
	}

	/**
	 * 根据日期获取工作记录
	 * @param {String} date 日期 (YYYY-MM-DD)
	 * @returns {Array} 工作记录列表
	 */
	getWorkRecordsByDate(date) {
		const allWorkRecords = uni.getStorageSync('workRecords') || []
		return allWorkRecords.filter(record => record.date === date)
	}

	/**
	 * 从工作记录计算产量 (向后兼容方法)
	 * @param {Object} workRecord 工作记录
	 * @returns {Number} 产量
	 */
	calculateWorkRecordProduction(workRecord) {
		if (workRecord.work_mode === 'tea_picking' && workRecord.tea_picking_details) {
			if (Array.isArray(workRecord.tea_picking_details)) {
				return workRecord.tea_picking_details.reduce((total, detail) => {
					return total + (parseFloat(detail.actual_weight) || 0)
				}, 0)
			} else if (typeof workRecord.tea_picking_details === 'object') {
				return parseFloat(workRecord.tea_picking_details.actual_weight) || 0
			}
		}
		return 0
	}

	/**
	 * 从工作记录创建默认销售记录
	 * @param {Object} workRecord 工作记录
	 * @returns {Object} 操作结果
	 */
	async createDefaultSalesRecordFromWorkRecord(workRecord) {
		try {
			// 检查是否已存在销售记录
			const existingSalesRecords = this.salesDB.getSalesRecordsByWorkRecordId(workRecord.id)
			if (existingSalesRecords.length > 0) {
				return {
					success: false,
					message: '该工作记录已存在销售记录'
				}
			}

			// 只为采茶记录创建销售记录
			if (workRecord.work_mode !== 'tea_picking') {
				return {
					success: false,
					message: '只能为采茶记录创建销售记录'
				}
			}

			// 计算产量
			const production = this.salesDB.calculateProductionFromWorkRecord(workRecord)
			if (production <= 0) {
				return {
					success: false,
					message: '工作记录没有有效的产量数据'
				}
			}

			// 创建默认销售记录
			const defaultSalesData = {
				work_record_id: workRecord.id,
				selling_price: 0, // 默认单价为0，需要用户填写
				production: production,
				other_income: 0,
				other_cost: 0,
				customer: '',
				notes: `从工作记录自动创建 - ${workRecord.worker_name} ${workRecord.date}`
			}

			const result = await this.salesDB.createSalesRecord(defaultSalesData)
			return {
				success: true,
				data: result,
				message: '默认销售记录创建成功'
			}
		} catch (error) {
			console.error('从工作记录创建默认销售记录失败:', error)
			return {
				success: false,
				message: error.message || '创建失败'
			}
		}
	}

	/**
	 * 获取销售统计信息
	 * @param {Object} dateRange 日期范围 {startDate, endDate}
	 * @returns {Object} 统计信息
	 */
	getSalesStatistics(dateRange = {}) {
		try {
			let salesRecords

			if (dateRange.startDate && dateRange.endDate) {
				salesRecords = this.getSalesRecordsByDateRange(dateRange.startDate, dateRange.endDate)
			} else {
				salesRecords = this.salesDB.getAllSalesRecords()
			}

			const stats = {
				totalRecords: salesRecords.length,
				totalProduction: 0,
				totalIncome: 0,
				totalCost: 0,
				totalGrossProfit: 0,
				avgSellingPrice: 0,
				avgGrossProfit: 0,
				profitMargin: 0,
				customerCount: 0,
				topCustomers: []
			}

			if (salesRecords.length === 0) {
				return stats
			}

			// 计算基础统计
			salesRecords.forEach(record => {
				stats.totalProduction += record.production || 0
				stats.totalIncome += record.total_income || 0
				stats.totalCost += record.total_cost || 0
				stats.totalGrossProfit += record.gross_profit || 0
			})

			// 计算平均值
			stats.avgSellingPrice = stats.totalProduction > 0 ? stats.totalIncome / stats.totalProduction : 0
			stats.avgGrossProfit = stats.totalRecords > 0 ? stats.totalGrossProfit / stats.totalRecords : 0
			stats.profitMargin = stats.totalIncome > 0 ? (stats.totalGrossProfit / stats.totalIncome) * 100 : 0

			// 统计客户信息
			const customerMap = new Map()
			salesRecords.forEach(record => {
				if (record.customer) {
					const customer = record.customer.trim()
					if (customer) {
						const existing = customerMap.get(customer) || { name: customer, count: 0, totalIncome: 0 }
						existing.count += 1
						existing.totalIncome += record.total_income || 0
						customerMap.set(customer, existing)
					}
				}
			})

			stats.customerCount = customerMap.size
			stats.topCustomers = Array.from(customerMap.values())
				.sort((a, b) => b.totalIncome - a.totalIncome)
				.slice(0, 5)

			return stats
		} catch (error) {
			console.error('获取销售统计信息失败:', error)
			return {
				totalRecords: 0,
				totalProduction: 0,
				totalIncome: 0,
				totalCost: 0,
				totalGrossProfit: 0,
				avgSellingPrice: 0,
				avgGrossProfit: 0,
				profitMargin: 0,
				customerCount: 0,
				topCustomers: []
			}
		}
	}

	/**
	 * 删除销售记录
	 * @param {String} salesRecordId 销售记录ID
	 * @returns {Object} 操作结果
	 */
	async deleteSalesRecord(salesRecordId) {
		try {
			const result = this.salesDB.deleteSalesRecord(salesRecordId)
			return {
				success: true,
				data: result,
				message: '销售记录删除成功'
			}
		} catch (error) {
			console.error('删除销售记录失败:', error)
			return {
				success: false,
				message: error.message || '删除失败'
			}
		}
	}

	/**
	 * 智能产量分配
	 * @param {String} date 日期
	 * @param {Array} customers 客户列表
	 * @returns {Object} 分配结果
	 */
	async smartProductionAllocation(date, customers) {
		try {
			if (!Array.isArray(customers) || customers.length === 0) {
				throw new Error('客户列表不能为空')
			}

			// 获取智能分配方案
			const allocations = this.salesDB.calculateSmartAllocation(date, customers.length)

			if (allocations.length === 0) {
				throw new Error('无法生成分配方案')
			}

			// 应用分配方案到客户数据
			const allocatedCustomers = customers.map((customer, index) => {
				const allocation = allocations.find(a => a.customerIndex === index)

				if (allocation) {
					return {
						...customer,
						production: allocation.production,
						time_period_allocation: allocation.timePeriodAllocation,
						allocation_strategy: allocation.strategy,
						allocation_priority: index + 1
					}
				}

				return customer
			})

			return {
				success: true,
				data: allocatedCustomers,
				allocations: allocations,
				message: '智能分配完成'
			}
		} catch (error) {
			console.error('智能产量分配失败:', error)
			return {
				success: false,
				message: error.message || '智能分配失败'
			}
		}
	}

	/**
	 * 验证产量分配
	 * @param {String} date 日期
	 * @param {Array} customers 客户列表
	 * @returns {Object} 验证结果
	 */
	validateProductionAllocation(date, customers) {
		try {
			const dailyProduction = this.salesDB.getDailyProduction(date)
			const totalAllocated = customers.reduce((sum, customer) => {
				return sum + (parseFloat(customer.production) || 0)
			}, 0)

			const isValid = totalAllocated <= dailyProduction
			const overLimit = Math.max(0, totalAllocated - dailyProduction)

			return {
				isValid: isValid,
				dailyProduction: dailyProduction,
				totalAllocated: totalAllocated,
				overLimit: overLimit,
				message: isValid
					? '产量分配正常'
					: `超出${overLimit.toFixed(2)}斤，请调整分配`
			}
		} catch (error) {
			console.error('验证产量分配失败:', error)
			return {
				isValid: false,
				message: '验证失败'
			}
		}
	}

	/**
	 * 获取销售模式统计
	 * @param {String} startDate 开始日期
	 * @param {String} endDate 结束日期
	 * @returns {Object} 统计结果
	 */
	getSalesModeStatistics(startDate, endDate) {
		try {
			const allRecords = this.salesDB.db.select('sales_records')

			const filteredRecords = allRecords.filter(record => {
				const recordDate = record.date
				return recordDate >= startDate && recordDate <= endDate
			})

			const statistics = {
				total: filteredRecords.length,
				singleCustomer: 0,
				multipleCustomers: 0,
				totalIncome: 0,
				totalProduction: 0,
				averagePrice: 0
			}

			filteredRecords.forEach(record => {
				const salesMode = record.sales_mode || 'single_customer'

				if (salesMode === 'single_customer') {
					statistics.singleCustomer++
				} else {
					statistics.multipleCustomers++
				}

				statistics.totalIncome += parseFloat(record.total_income) || 0
				statistics.totalProduction += parseFloat(record.production) || 0
			})

			if (statistics.totalProduction > 0) {
				statistics.averagePrice = statistics.totalIncome / statistics.totalProduction
			}

			return {
				success: true,
				data: statistics,
				message: '统计完成'
			}
		} catch (error) {
			console.error('获取销售模式统计失败:', error)
			return {
				success: false,
				message: error.message || '统计失败'
			}
		}
	}

	/**
	 * 根据日期删除销售记录（级联删除用）
	 * @param {String} date 日期 (YYYY-MM-DD格式)
	 * @returns {Object} 操作结果
	 */
	async deleteSalesRecordsByDate(date) {
		try {
			console.log('🗑️ [SalesManager] 开始删除日期的销售记录:', date)

			// 获取该日期的所有销售记录
			const salesRecords = this.salesDB.getSalesRecordsByDate(date)
			console.log('🗑️ [SalesManager] 找到销售记录数量:', salesRecords.length)

			if (salesRecords.length === 0) {
				return {
					success: true,
					data: [],
					message: '该日期没有销售记录需要删除'
				}
			}

			const deletedRecords = []
			let deleteErrors = []

			// 逐个删除销售记录
			for (const record of salesRecords) {
				try {
					const result = this.salesDB.deleteSalesRecord(record.id)
					deletedRecords.push(result)
					console.log('🗑️ [SalesManager] 已删除销售记录:', record.id)
				} catch (error) {
					console.error('🗑️ [SalesManager] 删除销售记录失败:', record.id, error)
					deleteErrors.push({
						recordId: record.id,
						error: error.message
					})
				}
			}

			if (deleteErrors.length > 0) {
				console.warn('🗑️ [SalesManager] 部分销售记录删除失败:', deleteErrors)
				return {
					success: false,
					data: deletedRecords,
					errors: deleteErrors,
					message: `删除了${deletedRecords.length}条记录，${deleteErrors.length}条记录删除失败`
				}
			}

			console.log('✅ [SalesManager] 日期销售记录删除完成:', {
				date: date,
				deletedCount: deletedRecords.length
			})

			return {
				success: true,
				data: deletedRecords,
				message: `成功删除${deletedRecords.length}条销售记录`
			}

		} catch (error) {
			console.error('🗑️ [SalesManager] 按日期删除销售记录失败:', error)
			return {
				success: false,
				message: error.message || '删除失败'
			}
		}
	}

	/**
	 * 检测孤立的销售记录（没有对应工作记录的销售记录）
	 * @param {String} date 可选，检查特定日期，不传则检查所有日期
	 * @returns {Array} 孤立的销售记录列表
	 */
	detectOrphanedSalesRecords(date = null) {
		try {
			console.log('🔍 [SalesManager] 开始检测孤立销售记录', date ? `日期: ${date}` : '全部日期')

			// 获取工作记录
			const workRecords = uni.getStorageSync('workRecords') || []

			// 获取销售记录
			let salesRecords = this.salesDB.getAllSalesRecords()

			// 如果指定了日期，只检查该日期的销售记录
			if (date) {
				salesRecords = salesRecords.filter(record => record.date === date)
			}

			const orphanedRecords = []

			// 检查每个销售记录是否有对应的工作记录
			for (const salesRecord of salesRecords) {
				const hasWorkRecord = workRecords.some(workRecord =>
					workRecord.date === salesRecord.date
				)

				if (!hasWorkRecord) {
					orphanedRecords.push(salesRecord)
					console.log('🔍 [SalesManager] 发现孤立销售记录:', {
						id: salesRecord.id,
						date: salesRecord.date,
						customer: salesRecord.customer_name,
						production: salesRecord.production
					})
				}
			}

			console.log('🔍 [SalesManager] 孤立销售记录检测完成:', {
				totalChecked: salesRecords.length,
				orphanedCount: orphanedRecords.length
			})

			return orphanedRecords

		} catch (error) {
			console.error('🔍 [SalesManager] 检测孤立销售记录失败:', error)
			return []
		}
	}

	/**
	 * 清理孤立的销售记录
	 * @param {String} date 可选，清理特定日期，不传则清理所有孤立记录
	 * @returns {Object} 清理结果
	 */
	async cleanupOrphanedSalesRecords(date = null) {
		try {
			console.log('🧹 [SalesManager] 开始清理孤立销售记录', date ? `日期: ${date}` : '全部日期')

			// 检测孤立记录
			const orphanedRecords = this.detectOrphanedSalesRecords(date)

			if (orphanedRecords.length === 0) {
				return {
					success: true,
					data: [],
					message: '没有发现孤立的销售记录'
				}
			}

			const cleanedRecords = []
			let cleanupErrors = []

			// 删除孤立记录
			for (const record of orphanedRecords) {
				try {
					const result = this.salesDB.deleteSalesRecord(record.id)
					cleanedRecords.push(result)
					console.log('🧹 [SalesManager] 已清理孤立销售记录:', record.id)
				} catch (error) {
					console.error('🧹 [SalesManager] 清理孤立销售记录失败:', record.id, error)
					cleanupErrors.push({
						recordId: record.id,
						error: error.message
					})
				}
			}

			if (cleanupErrors.length > 0) {
				console.warn('🧹 [SalesManager] 部分孤立销售记录清理失败:', cleanupErrors)
				return {
					success: false,
					data: cleanedRecords,
					errors: cleanupErrors,
					message: `清理了${cleanedRecords.length}条孤立记录，${cleanupErrors.length}条记录清理失败`
				}
			}

			console.log('✅ [SalesManager] 孤立销售记录清理完成:', {
				cleanedCount: cleanedRecords.length
			})

			return {
				success: true,
				data: cleanedRecords,
				message: `成功清理${cleanedRecords.length}条孤立销售记录`
			}

		} catch (error) {
			console.error('🧹 [SalesManager] 清理孤立销售记录失败:', error)
			return {
				success: false,
				message: error.message || '清理失败'
			}
		}
	}

	/**
	 * 批量创建销售记录
	 * @param {Array} salesDataList 销售记录数据列表
	 * @returns {Object} 操作结果
	 */
	async batchCreateSalesRecords(salesDataList) {
		try {
			const results = []
			const errors = []

			for (const salesData of salesDataList) {
				try {
					const result = await this.salesDB.createSalesRecord(salesData)
					results.push(result)
				} catch (error) {
					errors.push({
						data: salesData,
						error: error.message
					})
				}
			}

			return {
				success: errors.length === 0,
				data: results,
				errors: errors,
				message: `成功创建 ${results.length} 条记录，失败 ${errors.length} 条记录`
			}
		} catch (error) {
			console.error('批量创建销售记录失败:', error)
			return {
				success: false,
				message: error.message || '批量创建失败'
			}
		}
	}

	/**
	 * 验证销售记录数据完整性
	 * @returns {Object} 验证结果
	 */
	validateDataIntegrity() {
		try {
			const salesRecords = this.salesDB.getAllSalesRecords()
			const workRecords = uni.getStorageSync('workRecords') || []
			const issues = []

			// 检查孤立的销售记录
			salesRecords.forEach(salesRecord => {
				const workRecord = workRecords.find(wr => wr.id === salesRecord.work_record_id)
				if (!workRecord) {
					issues.push(`销售记录 ${salesRecord.id} 没有对应的工作记录`)
				}
			})

			// 检查数据类型和范围
			salesRecords.forEach(record => {
				if (typeof record.selling_price !== 'number' || record.selling_price < 0) {
					issues.push(`销售记录 ${record.id} 的销售单价无效`)
				}
				if (typeof record.production !== 'number' || record.production < 0) {
					issues.push(`销售记录 ${record.id} 的产量无效`)
				}
				if (typeof record.other_income !== 'number' || record.other_income < 0) {
					issues.push(`销售记录 ${record.id} 的其他收入无效`)
				}
				if (typeof record.other_cost !== 'number' || record.other_cost < 0) {
					issues.push(`销售记录 ${record.id} 的其他支出无效`)
				}
			})

			return {
				isValid: issues.length === 0,
				issues: issues,
				totalRecords: salesRecords.length
			}
		} catch (error) {
			console.error('验证数据完整性失败:', error)
			return {
				isValid: false,
				issues: ['验证过程中发生错误: ' + error.message],
				totalRecords: 0
			}
		}
	}
}

export default SalesManager
